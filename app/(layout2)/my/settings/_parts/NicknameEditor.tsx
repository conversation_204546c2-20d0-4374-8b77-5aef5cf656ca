"use client";

import React, { useState, useRef, useEffect } from "react";
import { AppButton } from "@/components";
import AppInput from "@/components/AppInput";
import { useDispatch } from "react-redux";
import rf from "@/services/RequestFactory";
import { successMsg, errorMsg } from "@/libs/toast";
import { setUserInfo } from "@/store/user.store";
import { TUser } from "@/store/user.store";
import { EditIcon } from "@/assets/icons";

interface NicknameEditorProps {
  userInfo: TUser;
}

const NicknameEditor: React.FC<NicknameEditorProps> = ({ userInfo }) => {
  const [isEditingNickname, setIsEditingNickname] = useState<boolean>(false);
  const [nickname, setNickname] = useState<string>("");
  const [originalNickname, setOriginalNickname] = useState<string>("");
  const [nicknameError, setNicknameError] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const dispatch = useDispatch();
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize nickname when userInfo changes
  useEffect(() => {
    if (userInfo?.nickname) {
      setNickname(userInfo.nickname);
      setOriginalNickname(userInfo.nickname);
    }
  }, [userInfo?.nickname]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditingNickname && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingNickname]);

  // Validation function
  const validateNickname = (
    value: string,
    isRealTime: boolean = false
  ): string => {
    const trimmedValue = value.trim();

    // For real-time validation, don't show "required" error when field is empty
    if (!trimmedValue) {
      return isRealTime ? "" : "Nickname is required";
    }

    if (trimmedValue.length < 5) {
      return "Nickname must be at least 5 characters long";
    }

    if (trimmedValue.length > 20) {
      return "Nickname must not exceed 20 characters";
    }

    // Check if it's only string characters (letters, numbers, spaces, basic symbols)
    const validPattern = /^[a-zA-Z0-9\s\-_\.]+$/;
    if (!validPattern.test(trimmedValue)) {
      return "Nickname can only contain letters, numbers, spaces, hyphens, underscores, and dots";
    }

    return "";
  };

  const handleNicknameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNickname(value);

    const error = validateNickname(value, true);
    setNicknameError(error);
  };

  const handleEditClick = () => {
    setIsEditingNickname(true);
    setNicknameError("");
  };

  const handleCancelEdit = () => {
    setIsEditingNickname(false);
    setNickname(originalNickname);
    setNicknameError("");
  };

  const handleSaveNickname = async () => {
    const trimmedNickname = nickname.trim();
    const error = validateNickname(trimmedNickname, false);

    if (error) {
      setNicknameError(error);
      return;
    }

    // Check if nickname actually changed
    if (trimmedNickname === originalNickname) {
      setIsEditingNickname(false);
      return;
    }

    setIsLoading(true);
    setNicknameError("");

    try {
      const response = await rf.getRequest("AccountRequest").updateNickname({
        nickname: trimmedNickname,
      });

      if (response?.success !== false) {
        // Update user info in store
        dispatch(
          setUserInfo({
            user: {
              ...userInfo,
              nickname: trimmedNickname,
            },
          })
        );

        setOriginalNickname(trimmedNickname);
        setIsEditingNickname(false);
        successMsg("Nickname updated successfully!");
      } else {
        const errorMessage = response?.error || "Failed to update nickname";
        setNicknameError(errorMessage);
        errorMsg(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        (error as Error)?.message || "Failed to update nickname";
      setNicknameError(errorMessage);
      errorMsg(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSaveNickname();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  const isFormValid = () => {
    const trimmedNickname = nickname.trim();
    return (
      trimmedNickname &&
      !validateNickname(trimmedNickname, false) &&
      trimmedNickname !== originalNickname
    );
  };

  if (isEditingNickname) {
    return (
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <AppInput
            ref={inputRef}
            value={nickname}
            onChange={handleNicknameChange}
            onKeyDown={handleKeyDown}
            className="body-sm-regular-12 min-w-[80px] px-2 py-1 text-xs"
            rootClassName="!p-0"
            placeholder="Enter nickname"
            disabled={isLoading}
          />
          <div className="flex gap-1">
            <AppButton
              variant="buy"
              size="small"
              onClick={handleSaveNickname}
              disabled={!isFormValid() || isLoading}
              className="px-2 py-1 text-xs"
            >
              {isLoading ? "..." : "Save"}
            </AppButton>
            <AppButton
              variant="secondary"
              size="small"
              onClick={handleCancelEdit}
              disabled={isLoading}
              className="px-2 py-1 text-xs"
            >
              Cancel
            </AppButton>
          </div>
        </div>
        {nicknameError && (
          <div className="text-[10px] text-red-600">{nicknameError}</div>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <div className="body-md-medium-14">{userInfo?.nickname}</div>
      <AppButton
        onClick={handleEditClick}
        variant="secondary"
        className="flex w-[80px] items-center gap-1"
      >
        <EditIcon /> Edit
      </AppButton>
    </div>
  );
};

export default NicknameEditor;
